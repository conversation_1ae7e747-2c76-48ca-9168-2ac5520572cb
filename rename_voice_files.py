import csv
import os

# Path to the CSV file
csv_file_path = '/media/hariswaqar/Data/tahir/wav2wec2_training/results/lesson7_summary_20250602_115757.csv'

# Directory containing the MP3 files
mp3_directory = '/media/hariswaqar/Data/tahir/voice_gen/Qari_lesson_7'

# Function to rename files
def rename_files(csv_file_path, mp3_directory):
    with open(csv_file_path, mode='r') as csv_file:
        csv_reader = csv.reader(csv_file)
        for row in csv_reader:
            if len(row) < 2:
                print(f'Skipping row due to insufficient columns: {row}')
                continue

            old_file_name, new_file_name = row[0].strip(), row[1].strip()

            # Construct full file paths
            old_file_path = os.path.join(mp3_directory, old_file_name)
            new_file_path = os.path.join(mp3_directory, f"{new_file_name}.mp3")

            # Check if the file exists and rename it
            if os.path.exists(old_file_path):
                os.rename(old_file_path, new_file_path)
                print(f'Renamed: {old_file_name} to {new_file_name}.mp3')
            else:
                print(f'File not found: {old_file_name}')

# Call the function to rename files
rename_files(csv_file_path, mp3_directory)
