import os
import torch
import json
import csv
from pathlib import Path
from datetime import datetime
from transformers import pipeline
from huggingface_hub import login
from tqdm import tqdm

device = "cuda" if torch.cuda.is_available() else "cpu"

HUGGINGFACE_TOKEN = os.getenv("HUGGINGFACE_HUB_TOKEN", "*************************************")

login(HUGGINGFACE_TOKEN)

lesson7 = pipeline("audio-classification", model="haris-waqar/qaida-Lesson7-classification", device=device)

def get_audio_files(folder_path):
    """Get all audio files from the specified folder."""
    folder = Path(folder_path)
    if not folder.exists():
        raise FileNotFoundError(f"Folder not found: {folder_path}")

    audio_extensions = ['.mp3', '.wav', '.flac', '.m4a', '.ogg']
    audio_files = []

    for ext in audio_extensions:
        audio_files.extend(folder.glob(f"*{ext}"))
        audio_files.extend(folder.glob(f"*{ext.upper()}"))

    return sorted(audio_files)

def classify_audio_file(file_path):
    """Classify a single audio file and return results."""
    try:
        result = lesson7(str(file_path))
        return {
            'file': file_path.name,
            'file_path': str(file_path),
            'predictions': result,
            'top_prediction': result[0] if result else None,
            'status': 'success',
            'error': None
        }
    except Exception as e:
        return {
            'file': file_path.name,
            'file_path': str(file_path),
            'predictions': None,
            'top_prediction': None,
            'status': 'error',
            'error': str(e)
        }

def process_folder(folder_path, output_dir="results"):
    """Process all audio files in the folder and generate reports."""
    print(f"Processing audio files in: {folder_path}")
    print(f"Using device: {device}")

    # Get all audio files
    audio_files = get_audio_files(folder_path)
    print(f"Found {len(audio_files)} audio files")

    if not audio_files:
        print("No audio files found in the specified folder.")
        return

    # Create output directory
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)

    # Process files
    results = []
    successful_classifications = 0
    failed_classifications = 0

    print("\nClassifying audio files...")
    for file_path in tqdm(audio_files, desc="Processing"):
        result = classify_audio_file(file_path)
        results.append(result)

        if result['status'] == 'success':
            successful_classifications += 1
        else:
            failed_classifications += 1
            print(f"Error processing {file_path.name}: {result['error']}")

    # Generate timestamp for output files
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save detailed results as JSON
    json_output = output_path / f"lesson7_classifications_{timestamp}.json"
    with open(json_output, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)

    # Save summary as CSV
    csv_output = output_path / f"lesson7_summary_{timestamp}.csv"
    with open(csv_output, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['File', 'Top_Label', 'Top_Score', 'Status', 'Error'])

        for result in results:
            if result['status'] == 'success' and result['top_prediction']:
                writer.writerow([
                    result['file'],
                    result['top_prediction']['label'],
                    result['top_prediction']['score'],
                    result['status'],
                    result['error'] or ''
                ])
            else:
                writer.writerow([
                    result['file'],
                    '',
                    '',
                    result['status'],
                    result['error'] or ''
                ])

    # Generate and save report
    report_output = output_path / f"lesson7_report_{timestamp}.txt"
    generate_report(results, successful_classifications, failed_classifications, report_output)

    print(f"\n=== Processing Complete ===")
    print(f"Total files processed: {len(audio_files)}")
    print(f"Successful classifications: {successful_classifications}")
    print(f"Failed classifications: {failed_classifications}")
    print(f"\nResults saved to:")
    print(f"  - Detailed JSON: {json_output}")
    print(f"  - Summary CSV: {csv_output}")
    print(f"  - Report: {report_output}")

    return results

def generate_report(results, successful, failed, output_file):
    """Generate a comprehensive report of the classification results."""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("=== Lesson 7 Audio Classification Report ===\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Device used: {device}\n\n")

        f.write("=== Summary ===\n")
        f.write(f"Total files processed: {len(results)}\n")
        f.write(f"Successful classifications: {successful}\n")
        f.write(f"Failed classifications: {failed}\n")
        f.write(f"Success rate: {(successful/len(results)*100):.1f}%\n\n")

        # Label distribution
        label_counts = {}
        for result in results:
            if result['status'] == 'success' and result['top_prediction']:
                label = result['top_prediction']['label']
                label_counts[label] = label_counts.get(label, 0) + 1

        if label_counts:
            f.write("=== Label Distribution ===\n")
            for label, count in sorted(label_counts.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / successful) * 100
                f.write(f"{label}: {count} files ({percentage:.1f}%)\n")
            f.write("\n")

        # Detailed results
        f.write("=== Detailed Results ===\n")
        for result in results:
            f.write(f"\nFile: {result['file']}\n")
            if result['status'] == 'success':
                f.write(f"Status: Success\n")
                if result['predictions']:
                    f.write("Predictions:\n")
                    for pred in result['predictions'][:3]:  # Top 3 predictions
                        f.write(f"  - {pred['label']}: {pred['score']:.4f}\n")
            else:
                f.write(f"Status: Failed\n")
                f.write(f"Error: {result['error']}\n")

if __name__ == "__main__":
    # Default folder path
    folder_path = "/media/hariswaqar/Data/tahir/voice_gen/Qari_lesson_7"

    # Process the folder
    try:
        results = process_folder(folder_path)
    except Exception as e:
        print(f"Error: {e}")
        print("Please check the folder path and try again.")